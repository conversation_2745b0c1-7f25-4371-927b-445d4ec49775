# AI助手桌面应用

一个美观专业的AI助手桌面应用程序，支持多种AI服务提供商，具有高性能、低内存占用和强扩展性的特点。

## 🚀 特性

- **模块化架构**: 采用解耦的模块化设计，易于维护和扩展
- **多AI服务支持**: 可扩展的AI服务提供商接口，支持ChatGPT、Claude、Gemini等
- **美观界面**: 现代化的用户界面设计，支持主题切换
- **高性能**: 优化的内存使用和响应速度
- **配置管理**: 完整的配置系统，支持个性化设置
- **实时对话**: 流畅的对话体验，支持历史记录

## 📁 项目结构

```
AI助手/
├── Scripts/
│   ├── Core/           # 核心系统
│   │   ├── AppManager.cs      # 应用程序管理器
│   │   └── AppConfig.cs       # 配置管理
│   ├── Services/       # 服务层
│   │   ├── IAIService.cs      # AI服务接口
│   │   ├── AIServiceManager.cs # AI服务管理器
│   │   └── MockAIService.cs   # 模拟AI服务
│   ├── UI/            # 用户界面
│   │   └── MainWindowController.cs # 主窗口控制器
│   ├── Models/        # 数据模型
│   └── Utils/         # 工具类
│       └── Logger.cs          # 日志工具
├── Scenes/
│   ├── Main.tscn      # 主场景
│   └── UI/
│       └── MainWindow.tscn    # 主窗口界面
├── Resources/
│   ├── Themes/        # 主题资源
│   ├── Icons/         # 图标资源
│   └── Fonts/         # 字体资源
└── project.godot      # Godot项目配置
```

## 🛠️ 技术栈

- **引擎**: Godot 4.5 (C# 支持)
- **语言**: C#
- **架构**: 模块化 + 服务导向架构 (SOA)
- **设计模式**: 单例模式、工厂模式、观察者模式

## 📋 核心组件

### AppManager (应用程序管理器)
- 单例模式的核心管理器
- 负责应用程序生命周期管理
- 服务注册和依赖注入
- 配置管理和主题应用

### AIServiceManager (AI服务管理器)
- 管理所有AI服务提供商
- 支持服务的动态注册和移除
- 统一的消息发送接口
- 服务能力查询和配置验证

### MainWindowController (主窗口控制器)
- 处理所有UI交互逻辑
- 消息显示和用户输入处理
- 服务选择和模型切换
- 实时状态更新

## 🎯 使用方法

### 1. 在Godot中打开项目
1. 启动Godot 4.5
2. 点击"导入"并选择项目文件夹
3. 等待项目导入完成

### 2. 生成C#项目
1. 在Godot编辑器中点击"项目" -> "工具" -> "C#" -> "创建C#解决方案"
2. 等待C#项目文件生成完成

### 3. 运行应用程序
1. 点击Godot编辑器右上角的"播放"按钮
2. 或按F5键运行项目

### 4. 使用界面
1. 在左侧边栏选择一个AI服务（ChatGPT、Claude、Gemini）
2. 在底部输入框中输入您的问题
3. 点击"发送"按钮或按Enter键发送消息
4. 查看AI的回复在聊天区域中显示

## 🔧 扩展开发

### 添加新的AI服务提供商

1. 实现`IAIService`接口：

```csharp
public class MyAIService : IAIService
{
    public string Name => "MyAI";
    public string Version => "1.0.0";
    public bool IsAvailable { get; private set; }
    public AIServiceCapabilities Capabilities { get; private set; }

    // 实现接口方法...
}
```

2. 在`AppManager`中注册服务：

```csharp
var myService = new MyAIService();
await serviceManager.RegisterServiceAsync(myService, config);
```

### 自定义主题

1. 在`Resources/Themes/`目录下创建新的主题文件
2. 在`AppConfig`中添加主题配置选项
3. 在`MainWindowController`中应用主题

### 添加新功能

1. 在相应的模块目录下创建新的类文件
2. 通过`AppManager`注册服务或组件
3. 在UI控制器中添加相应的交互逻辑

## 📝 配置说明

应用程序配置保存在`user://config.json`文件中，包含以下部分：

- **UI配置**: 主题、缩放、语言、窗口大小等
- **AI服务配置**: 默认服务、历史记录数量等
- **应用程序配置**: 版本信息、更新检查、自动保存等

## 🐛 故障排除

### 常见问题

1. **C#编译错误**: 确保已安装.NET SDK并在Godot中生成了C#解决方案
2. **服务无响应**: 检查网络连接和API密钥配置
3. **界面显示异常**: 尝试重置配置文件或重启应用程序

### 调试模式

在开发过程中，可以查看Godot编辑器的输出面板获取详细的日志信息。

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---

**注意**: 这是一个演示版本，包含模拟的AI服务。要连接真实的AI服务，需要实现相应的API调用逻辑并配置API密钥。
